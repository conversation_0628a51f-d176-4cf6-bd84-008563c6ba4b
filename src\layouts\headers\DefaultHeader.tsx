import Link from 'next/link';
import { useState } from 'react';
import { useRouter } from 'next/router';

interface DefaultHeaderProps {
  transparent?: boolean;
  headerTop?: boolean;
  extarClass?: string;
}

const DefaultHeader = ({
  transparent,
  headerTop,
  extarClass,
}: DefaultHeaderProps) => {
  const [toggle, setToggle] = useState(false);
  const router = useRouter();

  // Fonction pour déterminer si un lien est actif
  const isActiveLink = (href: string): boolean => {
    if (href === '/') {
      return router.pathname === '/';
    }
    return router.pathname.startsWith(href);
  };
  return (
    <div
      className={`mil-top-position mil-fixed ${extarClass ? extarClass : ''}`}
    >
      {headerTop && (
        <div className="mil-additional-panel">
          <div className="container-fluid">
            <ul className="mil-ap-list">
              <li>
                Téléphone: <span className="mil-accent">+237</span> 686 87 68 73
              </li>
              <li><EMAIL></li>
            </ul>
            <div className="mil-ap-call-to-action">
              <div className="mil-icon-frame mil-icon-frame-sm">
                <img src="img/icons/sm/4.svg" alt="icon" />
              </div>
              <p>
                Découvrez comment HREFF SARL peut transformer votre entreprise.
              </p>
            </div>
            <ul className="mil-ap-list">
              <li>
                <a href="#.">Douala, Cameroun</a>
              </li>
            </ul>
          </div>
        </div>
      )}
      <div
        className={`mil-top-panel ${
          transparent ? 'mil-top-panel-transparent mil-animated' : ''
        }`}
      >
        {/* mil-top-panel-transparent */}
        <div className="container">
          <Link href="/" legacyBehavior>
            <a className="mil-logo" style={{ width: 250 }}></a>
          </Link>
          <div className={`mil-navigation ${toggle ? 'mil-active' : ''}`}>
            <nav>
              <ul>
                <li>
                  <Link
                    href="/"
                    className={isActiveLink('/') ? 'mil-active' : ''}
                  >
                    Accueil
                  </Link>
                </li>
                <li>
                  <Link
                    href="/about"
                    className={isActiveLink('/about') ? 'mil-active' : ''}
                  >
                    À Propos
                  </Link>
                </li>
                <li>
                  <Link
                    href="/services"
                    className={isActiveLink('/services') ? 'mil-active' : ''}
                  >
                    Nos Services
                  </Link>
                </li>
                <li>
                  <Link
                    href="/portfolio"
                    className={isActiveLink('/portfolio') ? 'mil-active' : ''}
                  >
                    Portfolio
                  </Link>
                </li>
                <li>
                  <Link
                    href="/team"
                    className={isActiveLink('/team') ? 'mil-active' : ''}
                  >
                    Notre Équipe
                  </Link>
                </li>
                <li>
                  <Link
                    href="/contact"
                    className={isActiveLink('/contact') ? 'mil-active' : ''}
                  >
                    Contact
                  </Link>
                </li>
              </ul>
            </nav>
          </div>
          {/* mobile menu button */}
          <div
            className={`mil-menu-btn ${toggle ? 'mil-active' : ''}`}
            onClick={() => setToggle(!toggle)}
          >
            <span />
          </div>
          {/* mobile menu button end */}
        </div>
      </div>
    </div>
  );
};
export default DefaultHeader;
