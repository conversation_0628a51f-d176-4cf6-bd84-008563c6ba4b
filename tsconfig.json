{"compilerOptions": {"target": "es5", "module": "esnext", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "resolveJsonModule": true, "forceConsistentCasingInFileNames": true, "strict": true, "noEmit": true, "incremental": true, "isolatedModules": true, "moduleResolution": "node", "types": ["node"], "jsx": "preserve", "baseUrl": ".", "plugins": [{"name": "next"}], "paths": {"@/*": ["./*"], "@posts": ["./src/data/posts"], "@posts/*": ["./src/data/posts/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", "commitlint.config.ts", "src/common/itemsUtilits.ts"], "exclude": ["node_modules"]}