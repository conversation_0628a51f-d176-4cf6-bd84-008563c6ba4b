import CookieConsent from 'react-cookie-consent';
import Link from 'next/link';

export default function CookieBanner() {
  return (
    <CookieConsent
      location="bottom"
      buttonText="J'accepte"
      cookieName="HREFF_CookieConsent"
      style={{ background: '#2B373B' }}
      buttonStyle={{
        color: 'orange',
        background: '#fff',
        fontSize: '14px',
        fontWeight: 'bold',
        borderRadius: '2px',
      }}
      expires={150}
    >
      <p className="text-sm text-white">
        En utilisant ce site, vous acceptez nos{' '}
        <Link
          href="/conditions-utilisation"
          className="font-bold text-sky-400 underline"
        >
          Conditions d’utilisation
        </Link>{' '}
        et notre{' '}
        <Link
          href="/politique-confidentialite"
          className="font-bold text-sky-400 underline"
        >
          Politique de confidentialité
        </Link>
        . Nous utilisons également des{' '}
        <span className="font-bold text-sky-400">cookies</span> pour améliorer
        votre expérience.
      </p>
    </CookieConsent>
  );
}
