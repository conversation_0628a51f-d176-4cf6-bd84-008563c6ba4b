'use client';
import React, { useState, useEffect, useRef } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';

// Schéma de validation Zod
const newsletterSchema = z.object({
  email: z.string().email('Adresse email invalide'),
});

type NewsletterData = z.infer<typeof newsletterSchema>;

function NewsLetterForm() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitMessage, setSubmitMessage] = useState<{
    type: 'success' | 'error';
    message: string;
  } | null>(null);

  const messageRef = useRef<HTMLDivElement>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<NewsletterData>({
    resolver: zodResolver(newsletterSchema),
  });

  // Effet pour faire disparaître le message après 8s
  useEffect(() => {
    if (submitMessage) {
      const timer = setTimeout(() => {
        setSubmitMessage(null);
      }, 8000);
      return () => clearTimeout(timer);
    }
  }, [submitMessage]);

  // Fonction pour scroller vers le message
  const scrollToMessage = () => {
    if (messageRef.current) {
      messageRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
      });
    }
  };

  const onSubmit = async (data: NewsletterData) => {
    setIsSubmitting(true);
    setSubmitMessage(null);

    try {
      // Exemple : appel API (à adapter à ton backend)
      const response = await fetch('/api/newsletter', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error("Erreur lors de l'inscription à la newsletter");
      }

      await response.json();

      setSubmitMessage({
        type: 'success',
        message: 'Merci pour votre inscription ! 🎉',
      });

      reset(); // vider l'input
    } catch (error) {
      console.error('Erreur Newsletter:', error);
      setSubmitMessage({
        type: 'error',
        message: 'Impossible de vous inscrire pour le moment. Réessayez.',
      });
    } finally {
      setIsSubmitting(false);
      setTimeout(scrollToMessage, 50);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      {/* Message de statut */}
      {submitMessage && (
        <div
          ref={messageRef}
          className={`alert ${
            submitMessage.type === 'success' ? 'alert-success' : 'alert-danger'
          } mil-mb-15`}
        >
          {submitMessage.message}
        </div>
      )}

      <input
        {...register('email')}
        className={`mil-rounded-input mil-mb-5 ${errors.email ? 'error' : ''}`}
        type="email"
        placeholder="Votre adresse email"
        disabled={isSubmitting}
      />
      {errors.email && (
        <span className="error-message">{errors.email.message}</span>
      )}

      <button
        type="submit"
        className="mil-button mil-accent-bg mil-fw"
        disabled={isSubmitting}
      >
        <span>{isSubmitting ? 'Envoi...' : "S'abonner"}</span>
      </button>
    </form>
  );
}

export default NewsLetterForm;
