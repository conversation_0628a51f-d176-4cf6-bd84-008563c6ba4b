@import url('https://fonts.googleapis.com/css2?family=Satisfy&family=Sora:wght@100;200;300;400;500;600;700;800&family=Syne:wght@400;500;600;700;800&display=swap');

$font-1: 'Syne', sans-serif;
$font-2: 'Sora', sans-serif;
$font-3: 'Satisfy', cursive;

// Couleurs principales extraites du logo
$port-gore: rgba(36, 36, 82, 1); // Couleur principale sombre
$mirage: rgba(20, 20, 44, 1); // Couleur très sombre
$port-gore-alt: rgba(28, 33, 68, 1); // Variante port-gore
$port-gore-light: rgba(36, 40, 76, 1); // Variante plus claire

// Couleurs de base
$dark: $port-gore; // Utilise port-gore comme couleur sombre principale
$light: rgba(255, 255, 255, 1); // Blanc pour la version light
$accent: rgba(245, 124, 0, 1); // Garde l'accent orange existant

// Variantes d'opacité pour port-gore (couleur sombre principale)
$dt-5: rgba(36, 36, 82, 0.05);
$dt-10: rgba(36, 36, 82, 0.1);
$dt-20: rgba(36, 36, 82, 0.2);
$dt-30: rgba(36, 36, 82, 0.3);
$dt-40: rgba(36, 36, 82, 0.4);
$dt-50: rgba(36, 36, 82, 0.5);
$dt-60: rgba(36, 36, 82, 0.6);
$dt-70: rgba(36, 36, 82, 0.7);
$dt-80: rgba(36, 36, 82, 0.8);
$dt-90: rgba(36, 36, 82, 0.9);
$dt-95: rgba(36, 36, 82, 0.95);

// Variantes d'opacité pour le blanc (version light)
$lt-5: rgba(255, 255, 255, 0.05);
$lt-10: rgba(255, 255, 255, 0.1);
$lt-20: rgba(255, 255, 255, 0.2);
$lt-30: rgba(255, 255, 255, 0.3);
$lt-40: rgba(255, 255, 255, 0.4);
$lt-50: rgba(255, 255, 255, 0.5);
$lt-60: rgba(255, 255, 255, 0.6);
$lt-70: rgba(255, 255, 255, 0.7);
$lt-80: rgba(255, 255, 255, 0.8);
$lt-90: rgba(255, 255, 255, 0.9);
$lt-95: rgba(255, 255, 255, 0.95);

// Variantes d'opacité pour mirage (couleur très sombre)
$mirage-5: rgba(20, 20, 44, 0.05);
$mirage-10: rgba(20, 20, 44, 0.1);
$mirage-20: rgba(20, 20, 44, 0.2);
$mirage-30: rgba(20, 20, 44, 0.3);
$mirage-40: rgba(20, 20, 44, 0.4);
$mirage-50: rgba(20, 20, 44, 0.5);
$mirage-60: rgba(20, 20, 44, 0.6);
$mirage-70: rgba(20, 20, 44, 0.7);
$mirage-80: rgba(20, 20, 44, 0.8);
$mirage-90: rgba(20, 20, 44, 0.9);
$mirage-95: rgba(20, 20, 44, 0.95);

$h1: 60px;
$h2: 42px;
$h3: 26px;
$h4: 22px;
$h5: 18px;
$h6: 16px;

$text: 15px;

$t-sm: 0.2s cubic-bezier(0, 0, 0.3642, 1);
$t-md: 0.4s cubic-bezier(0, 0, 0.3642, 1);
$t-lg: 0.6s cubic-bezier(0, 0, 0.3642, 1);
