import fs from 'fs';
import path from 'path';
import matter from 'gray-matter';
import { remark } from 'remark';
import html from 'remark-html';
import slugify from 'slugify';
import type { ProjectSumary, PostFull } from './type';
import { z } from 'zod';

// Utility function to create SEO-friendly slugs

export const FrontMatterSchema = z.object({
  title: z.string(),
  tags: z.array(z.string()).optional(),
  author: z.string().optional(),
  short: z.string().optional(),
  image: z.string().optional(),
});

export type FrontMatter = z.infer<typeof FrontMatterSchema>;

const projectsDirectory = path.join(process.cwd(), 'src/data/projects');

export function getSortedProjectsData() {
  // Get file names under /posts
  const fileNames = fs.readdirSync(projectsDirectory);
  const allPostsData: ProjectSumary[] = fileNames.map((fileName) => {
    // Remove ".md" from file name to get id
    // const id = fileName.replace(/\.md$/, '');

    // Read markdown file as string
    const fullPath = path.join(projectsDirectory, fileName);
    const fileContents = fs.readFileSync(fullPath, 'utf8');

    // Use gray-matter to parse the post metadata section
    const matterResult = matter(fileContents);

    // ✅ Validation via Zod
    const data = FrontMatterSchema.parse(matterResult.data);
    const id = slugify(data.title, { lower: true });
    // Combine the data with the id
    return {
      id,
      fileName,
      ...data,
    };
  });
  // Sort posts by date
  return allPostsData.sort((a, b) => {
    const { birthtime: dateA } = fs.statSync(
      path.join(projectsDirectory, a.fileName),
    );
    const { birthtime: dateB } = fs.statSync(
      path.join(projectsDirectory, b.fileName),
    );
    if (dateA > dateB) {
      return 1;
    } else {
      return -1;
    }
  });
}

/** Retourne la liste des slugs pour `getStaticPaths` (Next .js pages) */
/** Retourne le contenu HTML et la front-matter d’un billet donné */
export async function getProjectData(
  id: string,
): Promise<Omit<PostFull, 'date'>> {
  const projects = await getSortedProjectsData();
  const selectedProject = projects.find((project) => project.id === id);

  if (!selectedProject) throw new Error('Id dosent match any project');

  const fileName = selectedProject.fileName;

  const fullPath = path.join(projectsDirectory, fileName);
  const fileContents = fs.readFileSync(fullPath, 'utf8');

  const matterResult = matter(fileContents);
  const data = FrontMatterSchema.parse(matterResult.data);
  const content = matterResult.content;

  const processed = await remark().use(html).process(content);

  return {
    id,
    contentHtml: processed.toString(),
    ...data,
  };
}
