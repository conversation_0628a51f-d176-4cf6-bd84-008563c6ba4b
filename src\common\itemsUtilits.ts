import fs from 'fs';
import path from 'path';
import matter from 'gray-matter';

// Type pour les métadonnées frontmatter
export interface ItemMeta {
  title: string;
  date: string;
  isFeatured?: boolean;
  [key: string]: any;
}

export interface ItemData extends ItemMeta {
  slug: string;
  content: string;
}

// Get all .md file names for a given type (e.g. "blog", "lessons")
export function getItemsFiles(type: string): string[] {
  const itemsDirectory = path.join(process.cwd(), 'src/data', type);
  return fs.readdirSync(itemsDirectory);
}

// Get the parsed data from a single Markdown file
export function getItemData(itemIdentifier: string, type: string): ItemData {
  const itemsDirectory = path.join(process.cwd(), 'src/data', type);
  const itemSlug = itemIdentifier.replace(/\.md$/, '');
  const filePath = path.join(itemsDirectory, `${itemSlug}.md`);
  const fileContent = fs.readFileSync(filePath, 'utf-8');

  const { data, content } = matter(fileContent);

  return {
    slug: itemSlug,
    ...(data as ItemMeta),
    content,
  };
}

// Get and sort all items by date descending
export function getAllItems(type: string): ItemData[] {
  const itemFiles = getItemsFiles(type);

  const allItems = itemFiles.map((itemFile) => getItemData(itemFile, type));

  const sortedItems = allItems.sort((itemA, itemB) =>
    itemA.date > itemB.date ? -1 : 1,
  );

  return sortedItems;
}

// Return only featured items
export function getFeaturedItems(items: ItemData[]): ItemData[] {
  return items.filter((item) => item.isFeatured);
}
