@tailwind base;
@tailwind components;
@tailwind utilities;

/* Styles pour le formulaire de contact */
.error-message {
  color: #dc3545;
  font-size: 0.875rem;
  margin-top: 0.25rem;
  display: block;
}

.mil-input-frame input.error,
.mil-input-frame textarea.error,
.mil-input-frame select.error {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.alert {
  padding: 1rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;
  border-radius: 0.375rem;
}

.alert-success {
  color: #155724;
  background-color: #d4edda;
  border-color: #c3e6cb;
}

.alert-danger {
  color: #721c24;
  background-color: #f8d7da;
  border-color: #f5c6cb;
}

.selected-files {
  margin-top: 0.75rem;
}

.file-list {
  list-style: none;
  padding: 0;
  margin: 0.5rem 0 0 0;
}

.file-list li {
  padding: 0.25rem 0;
  color: #6c757d;
}

.file-list i {
  margin-right: 0.5rem;
  color: #007bff;
}

.mil-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
