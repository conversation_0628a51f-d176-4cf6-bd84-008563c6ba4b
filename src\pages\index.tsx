import React from 'react';
import Layouts from '../layouts/Layouts';
import dynamic from 'next/dynamic';

import { getSortedProjectsData } from '../lib/projects';

import ServicesSection from '../components/sections/Services';
import HowItWorksSection from '../components/sections/HowItWorks';
import SkillsSection from '../components/sections/Skills';
import Divider from '../components/sections/Divider';
import { ProjectSumary } from '../lib/type';

const LatestProjectsSlider = dynamic(
  () => import('../components/sliders/LatestProjects'),
  { ssr: false },
);

const HeroSlideshowSlider = dynamic(
  () => import('../components/sliders/HeroSlideshow'),
  { ssr: false },
);
const TestimonialSlider = dynamic(
  () => import('../components/sliders/Testimonial'),
  { ssr: false },
);

interface Home1Props {
  projects: ProjectSumary[];
}

const Home1: React.FC<Home1Props> = (props) => {
  return (
    <Layouts transparent headerTop>
      <HeroSlideshowSlider />
      <ServicesSection />
      <Divider />
      <LatestProjectsSlider projects={props.projects} />
      <HowItWorksSection />
      <SkillsSection />
      <Divider />
      <TestimonialSlider />
    </Layouts>
  );
};
export default Home1;

export async function getStaticProps() {
  const allProjects = getSortedProjectsData();

  return {
    props: {
      projects: allProjects,
    },
  };
}
