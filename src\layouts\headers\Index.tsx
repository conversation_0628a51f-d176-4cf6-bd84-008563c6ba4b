import { stickyNav } from '@/src/common/utilits';
import { useEffect } from 'react';
import DefaultHeader from './DefaultHeader';

type HeaderProps = {
  transparent?: boolean;
  headerTop?: boolean;
  extarClass?: string;
};

const Header = ({ transparent, headerTop, extarClass }: HeaderProps) => {
  useEffect(() => {
    stickyNav(extarClass);
  }, [extarClass]);

  return (
    <DefaultHeader
      transparent={transparent}
      headerTop={headerTop}
      extarClass={extarClass}
    />
  );
};
export default Header;
