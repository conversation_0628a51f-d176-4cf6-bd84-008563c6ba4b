// src/lib/posts.ts

import { GrayMatterFile } from 'gray-matter';

export interface FrontMatter {
  /** Titre visible du billet */
  title: string;
  /** Date ISO 8601 (ex : 2025-05-16) */
  date: string;
  /** Éventuels tags */
  tags?: string[];
  /** Nom de l’auteur */
  author?: string;

  short?: string;
  /** Tout champ additionnel est ignoré mais conservé par gray-matter */
  [key: string]: unknown;
}

export interface PostSummary extends FrontMatter {
  /** slug dérivé du nom de fichier */
  id: string;
  /** Chemin vers l’image d’illustration */
  image?: string;
  /** Extrait du billet */
  short?: string;
  /** Nom de l’auteur */
  author?: string;
}

export interface ProjectSumary extends Omit<PostSummary, 'date'> {
  id: string;
  title: string;
  /** Chemin vers l’image d’illustration */
  image?: string;
  /** Extrait du billet */
  short?: string;
  /** Nom de l’auteur */
  author?: string;

  fileName: string;
}

export interface PostId {
  params: {
    id: string;
  };
}

export interface PostFull extends FrontMatter {
  id: string;
  /** HTML généré par remark */
  contentHtml: string;
}
