import PageBanner from '@/src/components/PageBanner';
import Layouts from '@/src/layouts/Layouts';
import Link from 'next/link';
import { useState, useEffect, useRef } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

// Schéma de validation Zod
const contactSchema = z.object({
  name: z.string().min(2, 'Le nom doit contenir au moins 2 caractères'),
  surname: z.string().min(2, 'Le prénom doit contenir au moins 2 caractères'),
  email: z.string().email('Adresse email invalide'),
  phone: z.string().min(8, 'Numéro de téléphone invalide'),
  company: z.string().optional(),
  role: z.string().optional(),
  serviceType: z.string().min(1, 'Veuillez sélectionner un type de service'),
  description: z
    .string()
    .min(10, 'La description doit contenir au moins 10 caractères'),
  budget: z.string().optional(),
  files: z.array(z.instanceof(File)).optional(),
});

type ContactFormData = z.infer<typeof contactSchema>;

// Types pour les fichiers joints
interface AttachmentFile {
  content: string; // Base64 string
  filename: string;
  content_type: string;
}

const Contact = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitMessage, setSubmitMessage] = useState<{
    type: 'success' | 'error';
    message: string;
  } | null>(null);

  const messageRef = useRef<HTMLDivElement>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset,
  } = useForm<ContactFormData>({
    resolver: zodResolver(contactSchema),
  });

  const watchedFiles = watch('files');

  // Effet pour faire disparaître le message après 10 secondes
  useEffect(() => {
    if (submitMessage) {
      const timer = setTimeout(() => {
        setSubmitMessage(null);
      }, 10000); // 10 secondes

      return () => clearTimeout(timer);
    }
  }, [submitMessage]);

  // Fonction pour scroller vers le message
  const scrollToMessage = () => {
    if (messageRef.current) {
      messageRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
      });
    }
  };

  // Fonction pour convertir un fichier en base64
  const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        const result = reader.result as string;
        // Retirer le préfixe "data:type/subtype;base64,"
        const base64 = result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = (error) => reject(error);
    });
  };

  // Fonction pour valider les types de fichiers
  const isValidFileType = (file: File): boolean => {
    const allowedTypes = [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif',
      'image/webp',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    ];
    return allowedTypes.includes(file.type);
  };

  // Gestion des fichiers
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    const validFiles = files.filter((file) => {
      if (!isValidFileType(file)) {
        setSubmitMessage({
          type: 'error',
          message: `Le fichier "${file.name}" n'est pas un type autorisé. Seuls les images (JPEG, PNG, GIF, WebP) et documents (PDF, DOC, DOCX) sont acceptés.`,
        });
        scrollToMessage();
        return false;
      }
      if (file.size > 40 * 1024 * 1024) {
        // 40MB
        setSubmitMessage({
          type: 'error',
          message: `Le fichier "${file.name}" est trop volumineux. La taille maximale est de 40MB.`,
        });
        scrollToMessage();
        return false;
      }
      return true;
    });

    setValue('files', validFiles);
    setSubmitMessage(null);
  };

  // Soumission du formulaire
  const onSubmit = async (data: ContactFormData) => {
    setIsSubmitting(true);
    setSubmitMessage(null);

    try {
      // Préparer les fichiers joints
      const attachments: AttachmentFile[] = [];
      if (data.files && data.files.length > 0) {
        for (const file of data.files) {
          const base64Content = await fileToBase64(file);
          attachments.push({
            content: base64Content,
            filename: file.name,
            content_type: file.type,
          });
        }
      }

      // Préparer les données à envoyer
      const formData = {
        ...data,
        attachments,
      };

      // Envoyer la requête POST
      const response = await fetch('/api/send-mail/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        throw new Error("Erreur lors de l'envoi du message");
      }

      await response.json();

      setSubmitMessage({
        type: 'success',
        message:
          'Votre message a été envoyé avec succès ! Nous vous répondrons dans les plus brefs délais.',
      });

      reset(); // Réinitialiser le formulaire
    } catch (error) {
      console.error("Erreur lors de l'envoi:", error);
      setSubmitMessage({
        type: 'error',
        message:
          "Une erreur est survenue lors de l'envoi de votre message. Veuillez réessayer.",
      });
    } finally {
      setIsSubmitting(false);
      setTimeout(() => {
        scrollToMessage();
      }, 50);
    }
  };

  return (
    <Layouts>
      <PageBanner
        pageName={'Contactez-nous'}
        pageTitle={'Let’s discuss your opportunity'}
      />
      {/* contact */}
      <section className="mil-contact mil-p-120-0">
        <div className="container">
          <div className="row justify-content-between">
            <div className="col-lg-8 col-xl-8 mil-mb-120">
              {/* Message de statut */}
              {submitMessage && (
                <div
                  ref={messageRef}
                  className={`alert ${submitMessage.type === 'success' ? 'alert-success' : 'alert-danger'} mil-mb-30`}
                >
                  {submitMessage.message}
                </div>
              )}

              <form onSubmit={handleSubmit(onSubmit)}>
                <h4 className="mil-mb-60">
                  <span className="mil-accent">01.</span> Parlez-nous de vous
                </h4>
                <div className="row">
                  <div className="col-lg-6">
                    <div className="mil-input-frame mil-dark-input mil-mb-30">
                      <label htmlFor="name" className="mil-h6 mil-dark">
                        <span>Nom *</span>
                      </label>
                      <input
                        {...register('name')}
                        type="text"
                        placeholder="Jean"
                        className={errors.name ? 'error' : ''}
                      />
                      {errors.name && (
                        <span className="error-message">
                          {errors.name.message}
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="col-lg-6">
                    <div className="mil-input-frame mil-dark-input mil-mb-30">
                      <label htmlFor="surname" className="mil-h6">
                        <span>Prénom *</span>
                      </label>
                      <input
                        {...register('surname')}
                        type="text"
                        placeholder="Dupont"
                        className={errors.surname ? 'error' : ''}
                      />
                      {errors.surname && (
                        <span className="error-message">
                          {errors.surname.message}
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="col-lg-6">
                    <div className="mil-input-frame mil-dark-input mil-mb-30">
                      <label htmlFor="email" className="mil-h6">
                        <span>Adresse Email *</span>
                      </label>
                      <input
                        {...register('email')}
                        type="email"
                        placeholder="<EMAIL>"
                        className={errors.email ? 'error' : ''}
                      />
                      {errors.email && (
                        <span className="error-message">
                          {errors.email.message}
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="col-lg-6">
                    <div className="mil-input-frame mil-dark-input mil-mb-30">
                      <label htmlFor="phone" className="mil-h6">
                        <span>Téléphone *</span>
                      </label>
                      <input
                        {...register('phone')}
                        type="tel"
                        placeholder="+237 6XX XX XX XX"
                        className={errors.phone ? 'error' : ''}
                      />
                      {errors.phone && (
                        <span className="error-message">
                          {errors.phone.message}
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="col-lg-6">
                    <div className="mil-input-frame mil-dark-input mil-mb-30">
                      <label htmlFor="company" className="mil-h6 mil-dark">
                        <span>Entreprise</span>
                      </label>
                      <input
                        {...register('company')}
                        type="text"
                        placeholder="Nom de votre entreprise"
                      />
                    </div>
                  </div>
                  <div className="col-lg-6 mil-mb-30">
                    <div className="mil-input-frame mil-dark-input mil-mb-30">
                      <label htmlFor="role" className="mil-h6 mil-dark">
                        <span>Fonction</span>
                      </label>
                      <input
                        {...register('role')}
                        type="text"
                        placeholder="Votre fonction"
                      />
                    </div>
                  </div>
                </div>
                <h4 className="mil-mb-60">
                  <span className="mil-accent">02.</span> Comment pouvons-nous
                  vous aider ?
                </h4>
                <div className="row">
                  <div className="col-lg-12 mil-mb-30">
                    <div className="mil-input-frame mil-dark-input mil-mb-30">
                      <label htmlFor="serviceType" className="mil-h6 mil-dark">
                        <span>Type de service souhaité *</span>
                      </label>
                      <select
                        {...register('serviceType')}
                        title="Type de service"
                        className={`mil-select ${errors.serviceType ? 'error' : ''}`}
                        id="serviceType"
                      >
                        <option value="">Sélectionnez un service</option>
                        <option value="developpement-web">
                          Développement Web
                        </option>
                        <option value="developpement-mobile">
                          Développement Mobile
                        </option>
                        <option value="logiciel-sur-mesure">
                          Logiciel sur Mesure
                        </option>
                        <option value="audit-systeme">
                          Audit des Systèmes IT
                        </option>
                        <option value="maintenance-it">Maintenance IT</option>
                        <option value="ecommerce">Solution E-commerce</option>
                        <option value="autre">Autre</option>
                      </select>
                      {errors.serviceType && (
                        <span className="error-message">
                          {errors.serviceType.message}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
                <h4 className="mil-mb-60">
                  <span className="mil-accent">03.</span> Parlez-nous de votre
                  projet
                </h4>
                <div className="row">
                  <div className="col-lg-12">
                    <div className="mil-input-frame mil-dark-input mil-mb-30">
                      <label htmlFor="description" className="mil-h6">
                        <span>Description du projet *</span>
                      </label>
                      <textarea
                        {...register('description')}
                        placeholder="Décrivez votre projet, vos besoins et vos objectifs..."
                        className={`mil-shortened ${errors.description ? 'error' : ''}`}
                      />
                      {errors.description && (
                        <span className="error-message">
                          {errors.description.message}
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="col-lg-12">
                    <div className="mil-attach-frame mil-dark mil-mb-30">
                      <i className="fas fa-paperclip" />
                      <label className="mil-custom-file-input">
                        <span>Joindre des fichiers (Images, PDF, DOC)</span>
                        <input
                          type="file"
                          id="mil-file-input"
                          multiple
                          accept="image/*,.pdf,.doc,.docx"
                          onChange={handleFileChange}
                        />
                      </label>
                      <p className="mil-text-sm mil-light-soft">
                        Formats acceptés: Images (JPEG, PNG, GIF, WebP), PDF,
                        DOC, DOCX - jusqu'à 40MB par fichier
                      </p>
                      {/* Affichage des fichiers sélectionnés */}
                      {watchedFiles && watchedFiles.length > 0 && (
                        <div className="selected-files mil-mt-15">
                          <p className="mil-text-sm mil-dark">
                            Fichiers sélectionnés:
                          </p>
                          <ul className="file-list">
                            {watchedFiles.map((file, index) => (
                              <li key={index} className="mil-text-sm">
                                <i className="fas fa-file"></i> {file.name} (
                                {(file.size / 1024 / 1024).toFixed(2)} MB)
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="col-lg-6 mil-mb-30">
                    <div className="mil-input-frame mil-dark-input mil-mb-30">
                      <label htmlFor="budget" className="mil-h6 mil-dark">
                        <span>Budget estimé (FCFA)</span>
                      </label>
                      <input
                        {...register('budget')}
                        type="number"
                        placeholder="Ex: 1000000"
                      />
                    </div>
                  </div>
                  <div className="col-lg-12">
                    <button
                      type="submit"
                      className="mil-button mil-border mil-fw"
                      disabled={isSubmitting}
                    >
                      <span>
                        {isSubmitting
                          ? 'Envoi en cours...'
                          : 'Envoyer la demande'}
                      </span>
                    </button>
                  </div>
                </div>
              </form>
            </div>
            <div className="col-lg-4 col-xl-3 mil-mb-120">
              <div className="mil-mb-60">
                <h5 className="mil-list-title mil-mb-30">Support Technique</h5>
                <p className="mil-mb-20">
                  Nos experts sont prêts à répondre à vos questions techniques
                  et commerciales.
                </p>
                <Link
                  href="mailto:<EMAIL>"
                  className="mil-link mil-link-sm"
                >
                  <span>Nous contacter</span>
                  <i className="fas fa-arrow-right" />
                </Link>
              </div>
              <div className="mil-divider mil-mb-60" />
              <div className="mil-mb-60">
                <div className="mil-icon-frame mil-icon-frame-md mil-icon-bg mil-mb-30">
                  <img src="img/icons/md/8.svg" alt="icon" />
                </div>
                <h5 className="mil-list-title mil-mb-30">Besoin d'aide ?</h5>
                <p>
                  Pour toute question technique ou commerciale, contactez notre
                  équipe dédiée au +237 686 87 68 73.
                </p>
              </div>
              <div className="mil-mb-60">
                <div className="mil-icon-frame mil-icon-frame-md mil-icon-bg mil-mb-30">
                  <img src="img/icons/md/9.svg" alt="icon" />
                </div>
                <h5 className="mil-list-title mil-mb-30">
                  Plus d'informations ?
                </h5>
                <p>
                  Découvrez nos services et solutions sur mesure. Nous sommes là
                  pour vous accompagner dans votre transformation digitale.
                </p>
              </div>
              <div className="mil-divider mil-mb-60" />
              <Link
                href="https://www.linkedin.com/in/high-refference"
                target="_blank"
                rel="noopener noreferrer"
                className="mil-link mil-link-sm mil-mb-15"
              >
                <span>Suivez-nous sur LinkedIn</span>
                <i className="fas fa-arrow-right" />
              </Link>
              <br />
              <Link href="tel:+237686876873" className="mil-link mil-link-sm">
                <span>Appelez-nous</span>
                <i className="fas fa-arrow-right" />
              </Link>
            </div>
          </div>
        </div>
      </section>
      {/* contact end */}
      {/* map */}
      <div>
        <div className="mil-map-frame">
          <iframe
            src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3979.6574052907117!2d9.74362207349145!3d4.089965946652371!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x10610d390016883d%3A0xe84ff13be9356421!2sYAPAKI%20PLUS%2B!5e0!3m2!1sfr!2scm!4v1755553123576!5m2!1sfr!2scm"
            width={600}
            height={450}
            style={{ border: 0 }}
            allowFullScreen
            loading="lazy"
            referrerPolicy="no-referrer-when-downgrade"
            title="HREFF SARL Location - Douala, Cameroon"
          />
        </div>
        <div className="container" />
      </div>
      {/* map end */}
      {/* contact info */}
      <section className="mil-p-120-60">
        <div className="container">
          <div className="row justify-content-center">
            <div className="col-xl-8">
              <div className="mil-mb-60 text-center">
                <h4 className="mil-mb-30">HIGH REFFERENCE SARL (HREFF SARL)</h4>
                <h5 className="mil-list-title mil-mb-15">
                  <span className="mil-accent">Innovons ensemble</span>
                </h5>
                <p className="mil-mb-30">
                  Votre partenaire stratégique pour des solutions numériques qui
                  transforment votre business
                  <br />
                  Siège social: Douala, Cameroun
                  <br />
                  Spécialisée dans le développement de solutions digitales sur
                  mesure
                </p>
                <div className="mil-divider mil-divider-center mil-mb-30" />
                <h6 className="mil-mb-15">
                  <i className="fas fa-phone mil-accent"></i>{' '}
                  <span className="mil-accent">+237</span> 686 87 68 73
                </h6>
                <h6 className="mil-mb-15">
                  <i className="fas fa-envelope mil-accent"></i>{' '}
                  <span className="mil-accent">contact</span>@hreff.com
                </h6>
                <h6 className="mil-mb-15">
                  <i className="fas fa-user mil-accent"></i>{' '}
                  <span className="mil-accent">CEO:</span> Stephan BOFIA
                </h6>
                <h6>
                  <i className="fas fa-map-marker-alt mil-accent"></i>{' '}
                  <span className="mil-accent">Localisation:</span> Douala,
                  Cameroun
                </h6>
              </div>
            </div>
          </div>
        </div>
      </section>
    </Layouts>
  );
};
export default Contact;
