export const stickyNav = (extraClass?: string): void => {
  window.addEventListener('scroll', () => {
    const offset = window.scrollY;
    const stickyElements = document.querySelectorAll<HTMLElement>(
      extraClass ? `.${extraClass}` : '.mil-animated',
    );

    stickyElements.forEach((stick) => {
      if (extraClass) {
        if (offset > 10) {
          stick.classList.add('mil-hide-top');
        } else {
          stick.classList.remove('mil-hide-top');
        }
      } else {
        if (offset > 10) {
          stick.classList.remove('mil-top-panel-transparent');
        } else {
          stick.classList.add('mil-top-panel-transparent');
        }
      }
    });
  });
};

export const milButtonClick = (): void => {
  const milTabButtons =
    document.querySelectorAll<HTMLAnchorElement>('.mil-tab-buttons a');
  milTabButtons.forEach((a) => {
    a.addEventListener('click', () => {
      milTabButtons.forEach((element) => {
        element.classList.remove('mil-active');
      });
      a.classList.add('mil-active');
    });
  });
};

export const milButtonClick2 = (): void => {
  const milTabButtons = document.querySelectorAll<HTMLAnchorElement>(
    '.mil-tabs-left-nav a',
  );
  milTabButtons.forEach((a) => {
    a.addEventListener('click', () => {
      milTabButtons.forEach((element) => {
        element.classList.remove('mil-active');
      });
      a.classList.add('mil-active');
    });
  });
};

export const accordion = (): void => {
  const acc = document.getElementsByClassName('mil-accordion');

  for (let i = 0; i < acc.length; i++) {
    const element = acc[i] as HTMLElement;

    element.addEventListener('click', (e) => {
      const target = e.currentTarget as HTMLElement;
      target.classList.toggle('mil-active');

      const panel = target.nextElementSibling as HTMLElement | null;
      if (panel) {
        panel.style.maxHeight = panel.style.maxHeight
          ? ''
          : `${panel.scrollHeight}px`;
      }
    });
  }
};
