import React from 'react';
import Head from 'next/head';
import './index.scss';
import '../styles/globals.css';

import { register } from 'swiper/element/bundle';
// register Swiper custom elements
register();

import type { AppProps } from 'next/app';
import CookieBanner from '../components/cookies-consent';

function MyApp({ Component, pageProps }: AppProps) {
  return (
    <>
      <Head>
        {/* seo begin */}
        <title>
          HREFF SARL - Solutions Digitales Innovantes | Innovons Ensemble
        </title>
        <meta
          name="description"
          content="HREFF SARL - Votre partenaire pour la transformation digitale au Cameroun. Développement logiciel, applications web et mobile, audit IT et maintenance système. Innovons ensemble."
        />
        <meta
          name="keywords"
          content="HREFF SARL, développement logiciel, applications web, applications mobile, audit IT, maintenance système, Douala, Cameroun, transformation digitale, JavaScript, e-commerce"
        />
        <meta name="author" content="HREFF SARL" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />

        {/* Open Graph / Facebook */}
        <meta property="og:type" content="website" />
        <meta
          property="og:title"
          content="HREFF SARL - Solutions Digitales Innovantes"
        />
        <meta
          property="og:description"
          content="Votre partenaire pour la transformation digitale au Cameroun. Développement logiciel, applications web et mobile, audit IT."
        />
        <meta property="og:locale" content="fr_FR" />

        {/* Twitter */}
        <meta property="twitter:card" content="summary_large_image" />
        <meta
          property="twitter:title"
          content="HREFF SARL - Solutions Digitales Innovantes"
        />
        <meta
          property="twitter:description"
          content="Votre partenaire pour la transformation digitale au Cameroun. Innovons ensemble."
        />
        {/* seo end */}
      </Head>
      <Component {...pageProps} />
      <CookieBanner />
    </>
  );
}

export default MyApp;
