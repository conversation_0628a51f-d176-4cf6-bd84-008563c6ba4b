import fs from 'node:fs';
import path from 'node:path';
import matter from 'gray-matter';
import { remark } from 'remark';
import html from 'remark-html';
import type { PostSummary, PostId, PostFull } from './type';
import { z } from 'zod';

export const FrontMatterSchema = z.object({
  title: z.string(),
  date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/), // ISO date format
  tags: z.array(z.string()).optional(),
  author: z.string().optional(),
  short: z.string().optional(),
  image: z.string().optional(),
});

export type FrontMatter = z.infer<typeof FrontMatterSchema>;

const postsDirectory = path.join(process.cwd(), 'src/data/posts');

export function getSortedPostsData(): PostSummary[] {
  const fileNames = fs.readdirSync(postsDirectory);

  const posts: PostSummary[] = fileNames.map((fileName) => {
    const id = fileName.replace(/\.md$/, '');

    const fullPath = path.join(postsDirectory, fileName);
    const fileContents = fs.readFileSync(fullPath, 'utf8');

    const matterResult = matter(fileContents);

    // ✅ Validation via Zod
    const data = FrontMatterSchema.parse(matterResult.data);

    return { id, ...data };
  });

  return posts.sort((a, b) => (a.date < b.date ? 1 : -1));
}

/** Retourne la liste des slugs pour `getStaticPaths` (Next .js pages) */
export function getAllPostIds(): PostId[] {
  return fs.readdirSync(postsDirectory).map<PostId>((fileName) => ({
    params: { id: fileName.replace(/\.md$/, '') },
  }));
}

/** Retourne le contenu HTML et la front-matter d’un billet donné */
export async function getPostData(id: string): Promise<PostFull> {
  const fullPath = path.join(postsDirectory, `${id}.md`);
  const fileContents = fs.readFileSync(fullPath, 'utf8');

  const matterResult = matter(fileContents);
  const data = FrontMatterSchema.parse(matterResult.data);
  const content = matterResult.content;

  const processed = await remark().use(html).process(content);

  return {
    id,
    contentHtml: processed.toString(),
    ...data,
  };
}
