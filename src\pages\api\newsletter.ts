import { NextApiRequest, NextApiResponse } from 'next';
import { Resend } from 'resend';
import { z } from 'zod';

// Initialiser Resend
const resend = new Resend(process.env.RESEND_API_KEY);
const AUDIENCE_ID = process.env.RESEND_AUDIENCE_ID!;
// Schéma pour la newsletter (seulement email)
const newsletterRequestSchema = z.object({
  email: z.email(),
});

type NewsletterRequest = z.infer<typeof newsletterRequestSchema>;

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      error: 'Méthode non autorisée. Utilisez POST.',
    });
  }

  try {
    // Validation
    const validatedData = newsletterRequestSchema.parse(req.body);

    // 1) Ajouter le contact à l’audience Resend
    await resend.contacts.create({
      audienceId: AUDIENCE_ID,
      email: validatedData.email,
    });

    // C<PERSON>er contenu de l’email
    const confirmationHtml = createNewsletterConfirmationEmailHtml();

    // Envoi avec Resend
    await resend.emails.send({
      from: '<EMAIL>',
      to: [validatedData.email],
      subject: 'Bienvenue dans la Newsletter HREFF',
      html: confirmationHtml,
    });

    return res.status(200).json({
      success: true,
      message: 'Email de confirmation envoyé avec succès',
    });
  } catch (error) {
    console.error("Erreur lors de l'envoi de l'email newsletter:", error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: 'Données invalides',
        details: z.prettifyError(error),
      });
    }

    return res.status(500).json({
      error: 'Erreur interne du serveur',
      message: "Impossible d'envoyer l'email de confirmation",
    });
  }
}

// Email HTML adapté pour la newsletter
function createNewsletterConfirmationEmailHtml(): string {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Bienvenue dans la Newsletter</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #007bff; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background-color: #f8f9fa; }
        .footer { text-align: center; padding: 20px; color: #6c757d; font-size: 14px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>Bienvenue 👋</h1>
          <p>Newsletter HREFF</p>
        </div>
        
        <div class="content">
          <p>Merci de vous être inscrit(e) à notre newsletter 🎉</p>
          
          <p>Vous recevrez bientôt nos dernières actualités, offres et conseils en transformation digitale directement dans votre boîte mail.</p>
          
          <p>À très vite,<br>
          L'équipe HREFF SARL</p>
        </div>
        
        <div class="footer">
          <p>HREFF SARL - Solutions Digitales sur Mesure</p>
        </div>
      </div>
    </body>
    </html>
  `;
}
